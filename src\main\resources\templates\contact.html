<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON><PERSON><PERSON>nur - İletişim">
    <meta name="keywords" content="<PERSON><PERSON><PERSON>, İletişim, Yaz<PERSON><PERSON>ım Mühendisi">
    <meta name="author" content="<PERSON><PERSON><PERSON>">
    
    <title><PERSON><PERSON><PERSON> Adanur - İletişim</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/images/favicon.png">
    
    <!-- Fontlar -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/css/style.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    <!-- Navigasyon -->
    <nav class="nav">
        <div class="nav__container container">
            <a href="/" class="nav__logo">İlker Adanur</a>
            
            <button class="nav__theme-toggle" aria-label="Tema değiştir">
                <i class="fas fa-moon"></i>
            </button>
            
            <ul class="nav__menu">
                <li><a href="/#about" class="nav__link">Hakkımda</a></li>
                <li><a href="/#skills" class="nav__link">Yetenekler</a></li>
                <li><a href="/timeline" class="nav__link">Projeler</a></li>
                <li><a href="/#contact" class="nav__link nav__link--active">İletişim</a></li>
                <li><a href="/calendar" class="nav__link">Takvim</a></li>
            </ul>
        </div>
    </nav>

    <!-- İletişim Bölümü -->
    <section class="contact" id="contact">
        <div class="container">
            <div class="contact__header text-center">
                <h1>İletişim</h1>
                <p class="text-secondary">Benimle iletişime geçin</p>
            </div>
            
            <div class="contact__grid">
                <div class="contact__info">
                    <div class="contact__card">
                        <div class="contact__item">
                            <i class="fas fa-envelope contact__icon"></i>
                            <div class="contact__details">
                                <h3>E-posta</h3>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                        </div>
                        
                        <div class="contact__item">
                            <i class="fab fa-github contact__icon"></i>
                            <div class="contact__details">
                                <h3>GitHub</h3>
                                <a href="https://github.com/ilkeradanur" target="_blank" rel="noopener">
                                    github.com/ilkeradanur
                                </a>
                            </div>
                        </div>
                        
                        <div class="contact__item">
                            <i class="fab fa-linkedin contact__icon"></i>
                            <div class="contact__details">
                                <h3>LinkedIn</h3>
                                <a href="https://linkedin.com/in/ilkeradanur" target="_blank" rel="noopener">
                                    linkedin.com/in/ilkeradanur
                                </a>
                            </div>
                        </div>
                        
                        <div class="contact__item">
                            <i class="fab fa-twitter contact__icon"></i>
                            <div class="contact__details">
                                <h3>Twitter</h3>
                                <a href="https://twitter.com/ilkeradanur" target="_blank" rel="noopener">
                                    @ilkeradanur
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact__availability">
                        <h3>Müsaitlik Durumu</h3>
                        <p>
                            Şu anda yeni projeler için müsaitim. 
                            Freelance işler veya tam zamanlı pozisyonlar için 
                            benimle iletişime geçebilirsiniz.
                        </p>
                        <div class="contact__status">
                            <span class="status-badge status-badge--available">
                                <i class="fas fa-circle"></i>
                                Müsait
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="contact__form-wrapper">
                    <form class="contact__form" id="contactForm">
                        <div class="form__group">
                            <label for="name" class="form__label">Adınız</label>
                            <input type="text" id="name" name="name" class="form__input" required
                                   placeholder="Adınızı girin">
                        </div>
                        
                        <div class="form__group">
                            <label for="email" class="form__label">E-posta</label>
                            <input type="email" id="email" name="email" class="form__input" required
                                   placeholder="E-posta adresinizi girin">
                        </div>
                        
                        <div class="form__group">
                            <label for="subject" class="form__label">Konu</label>
                            <input type="text" id="subject" name="subject" class="form__input" required
                                   placeholder="Mesajınızın konusunu girin">
                        </div>
                        
                        <div class="form__group">
                            <label for="message" class="form__label">Mesajınız</label>
                            <textarea id="message" name="message" class="form__input form__textarea" required
                                      placeholder="Mesajınızı girin" rows="5"></textarea>
                        </div>
                        
                        <button type="submit" class="form__button">
                            <i class="fas fa-paper-plane"></i>
                            Gönder
                        </button>
                    </form>
                    
                    <div class="form__success" id="formSuccess" style="display: none;">
                        <i class="fas fa-check-circle"></i>
                        <h3>Mesajınız Gönderildi!</h3>
                        <p>En kısa sürede size dönüş yapacağım.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer__content">
                <div class="footer__social">
                    <a href="https://github.com/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="GitHub">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://linkedin.com/in/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="LinkedIn">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="https://twitter.com/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
                <p>&copy; 2024 İlker Adanur. Tüm hakları saklıdır.</p>
            </div>
        </div>
        
        <button class="footer__back-to-top" aria-label="Yukarı çık">
            <i class="fas fa-arrow-up"></i>
        </button>
    </footer>

    <!-- JavaScript -->
    <script>
        // Tema değiştirme
        const themeToggle = document.querySelector('.nav__theme-toggle');
        const body = document.body;
        
        themeToggle.addEventListener('click', () => {
            body.dataset.theme = body.dataset.theme === 'dark' ? 'light' : 'dark';
            themeToggle.querySelector('i').classList.toggle('fa-moon');
            themeToggle.querySelector('i').classList.toggle('fa-sun');
        });

        // Yukarı çık butonu
        const backToTop = document.querySelector('.footer__back-to-top');
        
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTop.classList.add('footer__back-to-top--visible');
            } else {
                backToTop.classList.remove('footer__back-to-top--visible');
            }
        });
        
        backToTop.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Mobil menü
        const menuButton = document.querySelector('.nav__menu-button');
        const menu = document.querySelector('.nav__menu');
        
        menuButton?.addEventListener('click', () => {
            menu.classList.toggle('nav__menu--mobile');
        });

        // Form gönderimi
        const contactForm = document.getElementById('contactForm');
        const formSuccess = document.getElementById('formSuccess');
        
        contactForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            // Submit butonunu devre dışı bırak
            const submitButton = contactForm.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gönderiliyor...';
            
            // Form verilerini al
            const formData = new FormData(contactForm);
            const data = {
                name: formData.get('name'),
                email: formData.get('email'),
                subject: formData.get('subject'),
                message: formData.get('message')
            };

            try {
                const response = await fetch('/api/contact', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const responseText = await response.text();

                if (response.ok) {
                    contactForm.style.display = 'none';
                    formSuccess.style.display = 'block';
                    formSuccess.innerHTML = `
                        <i class="fas fa-check-circle"></i>
                        <h3>Mesajınız Gönderildi!</h3>
                        <p>${responseText}</p>
                    `;
                    
                    // Formu sıfırla
                    contactForm.reset();
                    
                    // 5 saniye sonra formu tekrar göster
                    setTimeout(() => {
                        contactForm.style.display = 'block';
                        formSuccess.style.display = 'none';
                    }, 5000);
                } else {
                    throw new Error(responseText || 'Mesaj gönderilirken bir hata oluştu.');
                }
            } catch (error) {
                // Hata mesajını göster
                const errorDiv = document.createElement('div');
                errorDiv.className = 'form__error';
                errorDiv.innerHTML = `
                    <i class="fas fa-exclamation-circle"></i>
                    <p>${error.message}</p>
                `;
                
                // Varsa eski hata mesajını kaldır
                const oldError = contactForm.querySelector('.form__error');
                if (oldError) oldError.remove();
                
                contactForm.insertBefore(errorDiv, submitButton);
                
                // 5 saniye sonra hata mesajını kaldır
                setTimeout(() => {
                    errorDiv.remove();
                }, 5000);
            } finally {
                // Submit butonunu tekrar aktif et
                submitButton.disabled = false;
                submitButton.innerHTML = '<i class="fas fa-paper-plane"></i> Gönder';
            }
        });
    </script>
</body>
</html> 