<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="İlk<PERSON> Adanur - Yazılım Mühendisi Kişisel Web Sitesi">
    <meta name="keywords" content="İlker Adanur, Yazılım Mühendisi, Java, Spring Boot, Web Geliştirme">
    <meta name="author" content="İlker Adanur">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://ilkeradanur.com/">
    <meta property="og:title" content="İlker Adanur - Yazılım Mühendisi">
    <meta property="og:description" content="Yazılım mühendisi İlker Adanur'un kişisel web sitesi">
    <meta property="og:image" content="https://ilkeradanur.com/images/profile.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://ilkeradanur.com/">
    <meta property="twitter:title" content="İlker Adanur - Yazılım Mühendisi">
    <meta property="twitter:description" content="Yazılım mühendisi İlker Adanur'un kişisel web sitesi">
    <meta property="twitter:image" content="https://ilkeradanur.com/images/profile.jpg">

    <title>İlker Adanur - Yazılım Mühendisi</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/images/favicon.png">
    
    <!-- Fontlar -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/css/style.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body data-theme="dark">
    <!-- Navigasyon -->
    <nav class="nav">
        <div class="nav__container container">
            <a href="/" class="nav__logo">İlker Adanur</a>
            
            <button class="nav__theme-toggle" aria-label="Tema değiştir">
                <i class="fas fa-moon"></i>
            </button>
            
            <ul class="nav__menu">
                <li><a href="#about" class="nav__link">Hakkımda</a></li>
                <li><a href="#skills" class="nav__link">Yetenekler</a></li>
                <li><a href="#projects" class="nav__link">Projeler</a></li>
                <li><a href="#timeline" class="nav__link">Zaman Çizelgesi</a></li>
                <li><a href="#contact" class="nav__link">İletişim</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Bölümü -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero__content">
                <div class="hero__text animate-fade-in">
                    <h1 class="hero__title">Merhaba, Ben İlker Adanur</h1>
                    <p class="hero__subtitle">Yazılım Mühendisi & Full Stack Geliştirici</p>
                    <p class="hero__description">
                        Modern web teknolojileri ve Java ekosistemi ile kullanıcı dostu, 
                        ölçeklenebilir uygulamalar geliştiriyorum.
                    </p>
                    <a href="#projects" class="hero__cta">
                        Projelerimi Gör
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
                <div class="hero__image animate-fade-in">
                    <img src="/images/profile.jpg" alt="İlker Adanur" class="hero__photo">
                </div>
            </div>
        </div>
    </section>

    <!-- Hakkımda Bölümü -->
    <section class="about" id="about">
        <div class="container">
            <h2 class="text-center mb-4">Hakkımda</h2>
            <div class="about__grid">
                <div class="about__content">
                    <p>
                        Merhaba! 3 yılı aşkın süredir yazılım geliştirme alanında tutkuyla çalışıyorum.
                        Java ve Spring Boot gibi güçlü teknolojilerle kurumsal düzeyde uygulamalar geliştirirken,
                        modern web teknolojilerini kullanarak kullanıcı odaklı ve etkileşimli arayüzler tasarlıyorum.
                    </p>
                    <p>
                        Teknolojinin hızla değişen dünyasında sürekli öğrenmeyi bir yaşam felsefesi olarak benimsedim.
                        Yeni gelişmeleri yakından takip ediyor, bilgilerimi taze tutuyor ve karşılaştığım zorlukları çözmek için
                        yaratıcı yaklaşımlar sergiliyorum. Aynı zamanda açık kaynak topluluğuna katkıda bulunarak
                        bilgi paylaşımına önem veriyorum.
                    </p>
                    <a href="/resume.pdf" class="hero__cta mt-3" download>
                        <i class="fas fa-download"></i>
                        CV'mi İndir
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Yetenekler Bölümü -->
    <section class="skills-section bg-surface" id="skills">
        <div class="container">
            <h2 class="text-center mb-4">Yeteneklerim</h2>
            
            <div class="skills__filters mb-4">
                <button class="filter__button filter__button--active" data-filter="all">Tümü</button>
                <button class="filter__button" data-filter="programming">Programlama</button>
                <button class="filter__button" data-filter="framework">Framework</button>
                <button class="filter__button" data-filter="database">Veritabanı</button>
                <button class="filter__button" data-filter="tool">Araç</button>
                <button class="filter__button" data-filter="language">Dil</button>
                <button class="filter__button" data-filter="certification">Sertifika</button>
            </div>
            
            <div class="skills__grid">
                <div class="skill" th:each="skill : ${skills}" 
                     th:data-category="${skill.category}"
                     th:data-proficiency="${skill.proficiency}">
                    <div class="skill__header">
                        <i th:class="${skill.icon}" class="skill__icon"></i>
                        <h3 class="skill__name" th:text="${skill.name}">Yetenek Adı</h3>
                        <div class="skill__certification" th:if="${skill.certificationName != null}">
                            <i class="fas fa-certificate"></i>
                            <a th:href="${skill.certificationUrl}" target="_blank" rel="noopener" 
                               th:text="${skill.certificationName}">Sertifika Adı</a>
                        </div>
                    </div>
                    <div class="skill__progress">
                        <div class="skill__bar" th:style="'width: ' + ${skill.proficiency} + '%'"></div>
                    </div>
                    <p class="skill__description" th:if="${skill.description != null}" th:text="${skill.description}">
                        Yetenek açıklaması
                    </p>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="/skills" class="hero__cta">
                    Tüm Yetenekleri Gör
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Projeler Bölümü -->
    <section class="projects" id="projects">
        <div class="container">
            <div class="projects__header">
                <h2>Projelerim</h2>
                <p class="text-secondary">Son geliştirdiğim projelerden bazıları</p>
            </div>
            
            <div class="projects__filters">
                <button class="filter__button filter__button--active" data-filter="all">Tümü</button>
                <button class="filter__button" data-filter="web">Web</button>
                <button class="filter__button" data-filter="mobile">Mobil</button>
                <button class="filter__button" data-filter="backend">Backend</button>
                <button class="filter__button" data-filter="desktop">Masaüstü</button>
                <button class="filter__button" data-filter="ai">Yapay Zeka</button>
            </div>
            
            <div class="projects__grid">
                <div class="project-card" th:each="project : ${projects}" 
                     th:data-category="${project.category}"
                     th:data-technologies="${project.technologies}"
                     th:data-status="${project.status}">
                    <div class="project-card__header">
                        <i class="fas" th:classappend="${project.category == 'web' ? 'fa-globe' : 
                                                      project.category == 'mobile' ? 'fa-mobile-alt' : 
                                                      project.category == 'backend' ? 'fa-server' : 
                                                      project.category == 'desktop' ? 'fa-desktop' : 
                                                      project.category == 'ai' ? 'fa-robot' : 'fa-code'}"></i>
                        <h3 th:text="${project.title}">Proje Başlığı</h3>
                    </div>
                    <p class="project-card__description" th:text="${project.description}">
                        Proje açıklaması
                    </p>
                    <div class="project-card__tags">
                        <span class="tag" th:each="tech : ${project.technologies.split(',')}" th:text="${tech.trim()}">Teknoloji</span>
                    </div>
                    <div class="project-card__links" th:if="${project.githubLink != null || project.demoLink != null}">
                        <a th:if="${project.githubLink != null}" th:href="${project.githubLink}" class="project-card__link" target="_blank" rel="noopener">
                            <i class="fab fa-github"></i> GitHub
                        </a>
                        <a th:if="${project.demoLink != null}" th:href="${project.demoLink}" class="project-card__link" target="_blank" rel="noopener">
                            <i class="fas fa-external-link-alt"></i> Demo
                        </a>
                    </div>
                    <div class="project-card__status" th:classappend="${project.status == T(com.ilkeradanur.personal_website.entity.ProjectStatus).COMPLETED ? 'status-completed' : 
                                                                     project.status == T(com.ilkeradanur.personal_website.entity.ProjectStatus).IN_PROGRESS ? 'status-in-progress' : 
                                                                     project.status == T(com.ilkeradanur.personal_website.entity.ProjectStatus).ON_HOLD ? 'status-on-hold' : 'status-cancelled'}">
                        <span th:text="${project.status.displayName}">Durum</span>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="/projects" class="hero__cta">
                    Tüm Projeleri Gör
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Zaman Çizelgesi Bölümü -->
    <section class="timeline-section bg-surface" id="timeline">
        <div class="container">
            <div class="timeline-section__header text-center">
                <h2>Zaman Çizelgesi</h2>
                <p class="text-secondary">Hayatımın önemli dönemleri ve projelerim</p>
            </div>
            
            <div class="timeline">
                <div class="timeline__period" th:each="project : ${projects}">
                    <div class="timeline__period-header">
                        <h3 th:text="${project.title}">Proje Başlığı</h3>
                        <p class="timeline__period-subtitle" th:text="${project.category}">Kategori</p>
                        <p class="timeline__period-date" th:if="${project.startDate != null}">
                            <span th:text="${#temporals.format(project.startDate, 'MMMM yyyy')}">Başlangıç Tarihi</span>
                            <span th:if="${project.endDate != null}" th:text="' - ' + ${#temporals.format(project.endDate, 'MMMM yyyy')}">Bitiş Tarihi</span>
                        </p>
                    </div>
                    
                    <div class="timeline__projects">
                        <div class="project-card" th:data-category="${project.category}" th:data-technologies="${project.technologies}" th:data-status="${project.status}">
                            <div class="project-card__header">
                                <i class="fas" th:classappend="${project.category == 'web' ? 'fa-globe' : 
                                                              project.category == 'mobile' ? 'fa-mobile-alt' : 
                                                              project.category == 'backend' ? 'fa-server' : 
                                                              project.category == 'desktop' ? 'fa-desktop' : 
                                                              project.category == 'ai' ? 'fa-robot' : 'fa-code'}"></i>
                                <h4 th:text="${project.title}">Proje Başlığı</h4>
                            </div>
                            <p class="project-card__description" th:text="${project.description}">
                                Proje açıklaması
                            </p>
                            <div class="project-card__tags">
                                <span class="tag" th:each="tech : ${project.technologies.split(',')}" th:text="${tech.trim()}">Teknoloji</span>
                            </div>
                            <div class="project-card__links" th:if="${project.githubLink != null || project.demoLink != null}">
                                <a th:if="${project.githubLink != null}" th:href="${project.githubLink}" class="project-card__link" target="_blank" rel="noopener">
                                    <i class="fab fa-github"></i> GitHub
                                </a>
                                <a th:if="${project.demoLink != null}" th:href="${project.demoLink}" class="project-card__link" target="_blank" rel="noopener">
                                    <i class="fas fa-external-link-alt"></i> Demo
                                </a>
                            </div>
                            <div class="project-card__status" th:classappend="${project.status == T(com.ilkeradanur.personal_website.entity.ProjectStatus).COMPLETED ? 'status-completed' : 
                                                                           project.status == T(com.ilkeradanur.personal_website.entity.ProjectStatus).IN_PROGRESS ? 'status-in-progress' : 
                                                                           project.status == T(com.ilkeradanur.personal_website.entity.ProjectStatus).ON_HOLD ? 'status-on-hold' : 'status-cancelled'}">
                                <span th:text="${project.status.displayName}">Durum</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="/timeline" class="hero__cta">
                    Detaylı Zaman Çizelgesi
                    <i class="fas fa-calendar-alt"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- İletişim Bölümü -->
    <section class="contact" id="contact">
        <div class="container">
            <h2 class="text-center mb-4">İletişim</h2>
            <div class="contact__grid">
                <div class="contact__info">
                    <div class="contact__item">
                        <i class="fas fa-envelope contact__icon"></i>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                    <div class="contact__item">
                        <i class="fab fa-github contact__icon"></i>
                        <a href="https://github.com/ilkeradanur" target="_blank" rel="noopener">github.com/ilkeradanur</a>
                    </div>
                    <div class="contact__item">
                        <i class="fab fa-linkedin contact__icon"></i>
                        <a href="https://www.linkedin.com/in/ilker-adanur-software-engineer/" target="_blank" rel="noopener">linkedin.com/in/ilker-adanur-software-engineer/</a>
                    </div>
                </div>
                
                <form class="contact__form">
                    <div class="form__group">
                        <label for="name" class="form__label">Adınız</label>
                        <input type="text" id="name" class="form__input" required>
                    </div>
                    <div class="form__group">
                        <label for="email" class="form__label">E-posta</label>
                        <input type="email" id="email" class="form__input" required>
                    </div>
                    <div class="form__group">
                        <label for="message" class="form__label">Mesajınız</label>
                        <textarea id="message" class="form__input form__textarea" required></textarea>
                    </div>
                    <button type="submit" class="form__button">
                        Gönder
                        <i class="fas fa-paper-plane"></i>
                    </buttonn>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer__content">
                <div class="footer__social">
                    <a href="https://github.com/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="GitHub">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://www.linkedin.com/in/ilker-adanur-software-engineer/" class="social__link" target="_blank" rel="noopener" aria-label="LinkedIn">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="https://x.com/_ilkerAdanur" class="social__link" target="_blank" rel="noopener" aria-label="X">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
                <p>&copy; 2024 İlker Adanur. Tüm hakları saklıdır.</p>
            </div>
        </div>
        
        <button class="footer__back-to-top" aria-label="Yukarı çık">
            <i class="fas fa-arrow-up"></i>
        </button>
    </footer>

    <!-- JavaScript -->
    <script>
        // Tema değiştirme
        const themeToggle = document.querySelector('.nav__theme-toggle');
        const body = document.body;
        
        themeToggle.addEventListener('click', () => {
            body.dataset.theme = body.dataset.theme === 'dark' ? 'light' : 'dark';
            themeToggle.querySelector('i').classList.toggle('fa-moon');
            themeToggle.querySelector('i').classList.toggle('fa-sun');
        });

        // Yukarı çık butonu
        const backToTop = document.querySelector('.footer__back-to-top');
        
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTop.classList.add('footer__back-to-top--visible');
            } else {
                backToTop.classList.remove('footer__back-to-top--visible');
            }
        });
        
        backToTop.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Mobil menü
        const menuButton = document.querySelector('.nav__menu-button');
        const menu = document.querySelector('.nav__menu');
        
        menuButton?.addEventListener('click', () => {
            menu.classList.toggle('nav__menu--mobile');
        });
    </script>

    <script th:inline="javascript">
        // Thymeleaf değişkenlerini JavaScript'e aktar
        const projects = /*[[${projects}]]*/ [];
        const skills = /*[[${skills}]]*/ [];
        
        document.addEventListener('DOMContentLoaded', function() {
            // Proje filtreleme
            const projectFilterButtons = document.querySelectorAll('.projects__filters .filter__button');
            const projectCards = document.querySelectorAll('.project-card');
            
            projectFilterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    projectFilterButtons.forEach(btn => btn.classList.remove('filter__button--active'));
                    this.classList.add('filter__button--active');
                    
                    const filter = this.dataset.filter;
                    
                    projectCards.forEach(card => {
                        if (filter === 'all' || card.dataset.category === filter) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });
            
            // Yetenek filtreleme
            const skillFilterButtons = document.querySelectorAll('.skills__filters .filter__button');
            const skillCards = document.querySelectorAll('.skill');
            
            skillFilterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    skillFilterButtons.forEach(btn => btn.classList.remove('filter__button--active'));
                    this.classList.add('filter__button--active');
                    
                    const filter = this.dataset.filter;
                    
                    skillCards.forEach(card => {
                        if (filter === 'all' || card.dataset.category === filter) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>
</body>
</html> 