// <PERSON><PERSON>e
const themeToggle = document.querySelector('.nav__theme-toggle');
const themeIcon = themeToggle.querySelector('i');

function setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
}

// Sayfa yüklendiğinde tema kontrolü
document.addEventListener('DOMContentLoaded', () => {
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
});

themeToggle.addEventListener('click', toggleTheme);

// <PERSON><PERSON> menü
const menuToggle = document.querySelector('.menu-toggle');
const nav = document.querySelector('.nav');

menuToggle?.addEventListener('click', () => {
    nav.classList.toggle('nav--active');
    menuToggle.setAttribute('aria-expanded', 
        menuToggle.getAttribute('aria-expanded') === 'true' ? 'false' : 'true'
    );
});

// Sayfa içi linkler için smooth scroll
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        
        if (target) {
            // Mobil menüyü kapat
            nav?.classList.remove('nav--active');
            menuToggle?.setAttribute('aria-expanded', 'false');
            
            // Hedefe scroll
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Yukarı çık butonu
const backToTopButton = document.querySelector('.footer__back-to-top');

window.addEventListener('scroll', () => {
    if (window.pageYOffset > 300) {
        backToTopButton.classList.add('visible');
    } else {
        backToTopButton.classList.remove('visible');
    }
});

backToTopButton.addEventListener('click', () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});

// Modal işlemleri
const modal = document.querySelector('.modal');
const modalClose = document.querySelector('.modal__close');
const modalTriggers = document.querySelectorAll('[data-modal]');

// Modal açma fonksiyonu
function openModal(modalId) {
    const targetModal = document.querySelector(`#${modalId}`);
    if (targetModal) {
        targetModal.classList.add('modal--visible');
        document.body.style.overflow = 'hidden';
    }
}

// Modal kapatma fonksiyonu
function closeModal() {
    const visibleModal = document.querySelector('.modal--visible');
    if (visibleModal) {
        visibleModal.classList.remove('modal--visible');
        document.body.style.overflow = '';
    }
}

// Modal tetikleyicileri
modalTriggers.forEach(trigger => {
    trigger.addEventListener('click', (e) => {
        e.preventDefault();
        const modalId = trigger.dataset.modal;
        openModal(modalId);
    });
});

// Modal kapatma butonu
modalClose?.addEventListener('click', closeModal);

// Modal dışına tıklama ile kapatma
modal?.addEventListener('click', (e) => {
    if (e.target === modal) {
        closeModal();
    }
});

// ESC tuşu ile modal kapatma
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        closeModal();
    }
});

// Form gönderimi
const contactForm = document.querySelector('.contact__form');

contactForm?.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    // Submit butonunu devre dışı bırak
    const submitButton = contactForm.querySelector('button[type="submit"]');
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gönderiliyor...';
    
    // Form verilerini al
    const formData = new FormData(contactForm);
    const data = {
        name: formData.get('name'),
        email: formData.get('email'),
        subject: formData.get('subject'),
        message: formData.get('message')
    };
    
    try {
        // Form verilerini JSON olarak gönder
        const response = await fetch('/api/contact', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const responseText = await response.text();
        
        if (response.ok) {
            // Form başarılı mesajını göster
            contactForm.style.display = 'none';
            const formSuccess = document.querySelector('.form__success');
            formSuccess.style.display = 'block';
            formSuccess.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <h3>Mesajınız Gönderildi!</h3>
                <p>${responseText}</p>
            `;
            
            // Formu sıfırla
            contactForm.reset();
            
            // 5 saniye sonra formu tekrar göster
            setTimeout(() => {
                contactForm.style.display = 'block';
                formSuccess.style.display = 'none';
            }, 5000);
        } else {
            throw new Error(responseText || 'Form gönderilemedi');
        }
    } catch (error) {
        console.error('Form gönderimi sırasında hata:', error);
        
        // Hata mesajını göster
        const errorDiv = document.createElement('div');
        errorDiv.className = 'form__error';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            <p>${error.message}</p>
        `;
        
        // Varsa eski hata mesajını kaldır
        const oldError = contactForm.querySelector('.form__error');
        if (oldError) oldError.remove();
        
        contactForm.insertBefore(errorDiv, submitButton);
        
        // 5 saniye sonra hata mesajını kaldır
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    } finally {
        // Submit butonunu tekrar aktif et
        submitButton.disabled = false;
        submitButton.innerHTML = '<i class="fas fa-paper-plane"></i> Gönder';
    }
});

// Proje filtreleme
function initializeProjectFilters() {
    const categoryFilter = document.getElementById('category-filter');
    const technologyFilter = document.getElementById('technology-filter');
    const statusFilter = document.getElementById('status-filter');
    const filterReset = document.getElementById('filter-reset');
    const projectCards = document.querySelectorAll('.project-card');

    if (!categoryFilter || !technologyFilter || !statusFilter || !filterReset) return;

    function filterProjects() {
        const selectedCategory = categoryFilter.value;
        const selectedTechnologies = Array.from(technologyFilter.selectedOptions).map(option => option.value);
        const selectedStatus = statusFilter.value;

        projectCards.forEach(card => {
            const category = card.dataset.category;
            const technologies = card.dataset.technologies.split(',').map(tech => tech.trim());
            const status = card.dataset.status;

            const categoryMatch = !selectedCategory || category === selectedCategory;
            const technologyMatch = selectedTechnologies.length === 0 || 
                                  selectedTechnologies.every(tech => technologies.includes(tech));
            const statusMatch = !selectedStatus || status === selectedStatus;

            card.style.display = categoryMatch && technologyMatch && statusMatch ? 'block' : 'none';
        });

        // URL parametrelerini güncelle
        const params = new URLSearchParams(window.location.search);
        if (selectedCategory) params.set('category', selectedCategory);
        else params.delete('category');
        
        if (selectedTechnologies.length) params.set('technologies', selectedTechnologies.join(','));
        else params.delete('technologies');
        
        if (selectedStatus) params.set('status', selectedStatus);
        else params.delete('status');

        const newUrl = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`;
        window.history.replaceState({}, '', newUrl);
    }

    // URL parametrelerinden filtreleri yükle
    function loadFiltersFromUrl() {
        const params = new URLSearchParams(window.location.search);
        
        const category = params.get('category');
        if (category) categoryFilter.value = category;
        
        const technologies = params.get('technologies');
        if (technologies) {
            const techArray = technologies.split(',');
            Array.from(technologyFilter.options).forEach(option => {
                option.selected = techArray.includes(option.value);
            });
        }
        
        const status = params.get('status');
        if (status) statusFilter.value = status;

        filterProjects();
    }

    // Event listeners
    categoryFilter.addEventListener('change', filterProjects);
    technologyFilter.addEventListener('change', filterProjects);
    statusFilter.addEventListener('change', filterProjects);
    
    filterReset.addEventListener('click', () => {
        categoryFilter.value = '';
        technologyFilter.value = '';
        statusFilter.value = '';
        filterProjects();
    });

    // Sayfa yüklendiğinde URL'den filtreleri yükle
    loadFiltersFromUrl();
}

// İstatistik grafiklerini güncelle
function updateStatisticsCharts() {
    const categoryChart = document.getElementById('category-chart');
    const technologyChart = document.getElementById('technology-chart');

    if (!categoryChart || !technologyChart) return;

    // Kategori grafiğini güncelle
    const categoryBars = categoryChart.querySelectorAll('.chart-bar');
    categoryBars.forEach(bar => {
        const value = parseInt(bar.dataset.value);
        const total = parseInt(categoryChart.dataset.total);
        const percentage = (value / total) * 100;
        bar.style.width = `${percentage}%`;
    });

    // Teknoloji grafiğini güncelle
    const technologyBars = technologyChart.querySelectorAll('.chart-bar');
    technologyBars.forEach(bar => {
        const value = parseInt(bar.dataset.value);
        const total = parseInt(technologyChart.dataset.total);
        const percentage = (value / total) * 100;
        bar.style.width = `${percentage}%`;
    });
}

// Sayfa yüklendiğinde proje filtrelerini ve grafikleri başlat
document.addEventListener('DOMContentLoaded', () => {
    initializeProjectFilters();
    updateStatisticsCharts();
});

// Animasyonlar için Intersection Observer
const animateOnScroll = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate');
            animateOnScroll.unobserve(entry.target);
        }
    });
}, {
    threshold: 0.1
});

// Animasyonlu elementleri gözlemle
document.querySelectorAll('.animate-on-scroll').forEach(element => {
    animateOnScroll.observe(element);
});

// Sayfa yüklendiğinde animasyonları başlat
document.addEventListener('DOMContentLoaded', () => {
    // Hero section animasyonu
    const hero = document.querySelector('.hero');
    if (hero) {
        hero.classList.add('animate');
    }
    
    // Diğer animasyonlu elementler
    document.querySelectorAll('.animate-on-scroll').forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
    });
}); 