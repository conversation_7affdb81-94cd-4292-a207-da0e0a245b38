<!DOCTYPE html>
<html lang="tr" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON><PERSON><PERSON>">
    <meta name="keywords" content="<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>jeleri, Web Geliştirme, Mobil Uygulama">
    <meta name="author" content="İlker Adanur">
    
    <title th:text="${title}"><PERSON><PERSON><PERSON>r - Projeler</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/images/favicon.png">
    
    <!-- Fontlar -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/css/style.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    <!-- Navigasyon -->
    <nav class="nav">
        <div class="nav__container container">
            <a href="/" class="nav__logo">İlker Adanur</a>
            
            <button class="nav__theme-toggle" aria-label="Tema değiştir">
                <i class="fas fa-moon"></i>
            </button>
            
            <ul class="nav__menu">
                <li><a href="/#about" class="nav__link">Hakkımda</a></li>
                <li><a href="/#skills" class="nav__link">Yetenekler</a></li>
                <li><a href="/projects" class="nav__link nav__link--active">Projeler</a></li>
                <li><a href="/#contact" class="nav__link">İletişim</a></li>
                <li><a href="/projects/timeline" class="nav__link">Zaman Çizelgesi</a></li>
            </ul>
        </div>
    </nav>

    <!-- Projeler Bölümü -->
    <section class="projects-section" id="projects">
        <div class="container">
            <div class="projects-section__header text-center">
                <h1 th:text="${title}">Projelerim</h1>
                <p class="text-secondary">Geliştirdiğim projeler ve çalışmalarım</p>
            </div>

            <!-- Filtreler -->
            <div class="projects-section__filters">
                <div class="filter-group">
                    <label for="category-filter">Kategori:</label>
                    <select id="category-filter" class="filter-select">
                        <option value="">Tümü</option>
                        <option th:each="category : ${categories}" 
                                th:value="${category}" 
                                th:text="${category.displayName}">Kategori</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="technology-filter">Teknoloji:</label>
                    <select id="technology-filter" class="filter-select" multiple>
                        <option th:each="tech : ${technologies}" 
                                th:value="${tech}" 
                                th:text="${tech}">Teknoloji</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="status-filter">Durum:</label>
                    <select id="status-filter" class="filter-select">
                        <option value="">Tümü</option>
                        <option th:each="status : ${statuses}" 
                                th:value="${status}" 
                                th:text="${status.displayName}">Durum</option>
                    </select>
                </div>

                <button id="filter-reset" class="btn btn-secondary">
                    <i class="fas fa-undo"></i> Filtreleri Sıfırla
                </button>
            </div>

            <!-- Proje Kartları -->
            <div class="projects-grid">
                <div class="project-card" th:each="project : ${projects}" 
                     th:data-category="${project.category}"
                     th:data-technologies="${project.technologies}"
                     th:data-status="${project.status}">
                    <div class="project-card__header">
                        <i class="fas" th:classappend="${project.category == 'web' ? 'fa-globe' : 
                                                      project.category == 'mobile' ? 'fa-mobile-alt' : 
                                                      project.category == 'backend' ? 'fa-server' : 
                                                      project.category == 'desktop' ? 'fa-desktop' : 
                                                      project.category == 'ai' ? 'fa-robot' : 'fa-code'}"></i>
                        <h3 th:text="${project.title}">Proje Başlığı</h3>
                    </div>
                    <p class="project-card__description" th:text="${project.description}">
                        Proje açıklaması
                    </p>
                    <div class="project-card__tags">
                        <span class="tag" th:each="tech : ${project.technologies.split(',')}" th:text="${tech.trim()}">Teknoloji</span>
                    </div>
                    <div class="project-card__links" th:if="${project.githubLink != null || project.demoLink != null}">
                        <a th:if="${project.githubLink != null}" th:href="${project.githubLink}" class="project-card__link" target="_blank" rel="noopener">
                            <i class="fab fa-github"></i> GitHub
                        </a>
                        <a th:if="${project.demoLink != null}" th:href="${project.demoLink}" class="project-card__link" target="_blank" rel="noopener">
                            <i class="fas fa-external-link-alt"></i> Demo
                        </a>
                    </div>
                    <div class="project-card__status" th:classappend="${project.status == 'COMPLETED' ? 'status-completed' : 
                                                                     project.status == 'IN_PROGRESS' ? 'status-in-progress' : 
                                                                     project.status == 'ON_HOLD' ? 'status-on-hold' : 'status-cancelled'}">
                        <span th:text="${project.status.displayName}">Durum</span>
                    </div>
                </div>
            </div>

            <!-- İstatistikler -->
            <div class="projects-section__stats">
                <div class="stats-card">
                    <h3>Kategori Dağılımı</h3>
                    <div class="stats-chart" id="category-chart">
                        <div class="chart-bar" th:each="stat : ${categoryStats}" 
                             th:style="'width: ' + ${stat.value * 100 / #stats.total} + '%'"
                             th:data-category="${stat.key}"
                             th:data-value="${stat.value}">
                            <span class="chart-label" th:text="${stat.key.displayName}">Kategori</span>
                            <span class="chart-value" th:text="${stat.value}">0</span>
                        </div>
                    </div>
                </div>

                <div class="stats-card">
                    <h3>Teknoloji Dağılımı</h3>
                    <div class="stats-chart" id="technology-chart">
                        <div class="chart-bar" th:each="stat : ${technologyStats}" 
                             th:style="'width: ' + ${stat.value * 100 / #stats.total} + '%'"
                             th:data-technology="${stat.key}"
                             th:data-value="${stat.value}">
                            <span class="chart-label" th:text="${stat.key}">Teknoloji</span>
                            <span class="chart-value" th:text="${stat.value}">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer__content">
                <div class="footer__social">
                    <a href="https://github.com/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="GitHub">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://linkedin.com/in/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="LinkedIn">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="https://twitter.com/ilkeradanur" class="social__link" target="_blank" rel="noopener" aria-label="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
                <p>&copy; 2024 İlker Adanur. Tüm hakları saklıdır.</p>
            </div>
        </div>
        
        <button class="footer__back-to-top" aria-label="Yukarı çık">
            <i class="fas fa-arrow-up"></i>
        </button>
    </footer>

    <!-- JavaScript -->
    <script src="/js/main.js"></script>
    <script th:inline="javascript">
        // Thymeleaf değişkenlerini JavaScript'e aktar
        const projects = /*[[${projects}]]*/ [];
        const categories = /*[[${categories}]]*/ [];
        const technologies = /*[[${technologies}]]*/ [];
        const statuses = /*[[${statuses}]]*/ [];
        const categoryStats = /*[[${categoryStats}]]*/ {};
        const technologyStats = /*[[${technologyStats}]]*/ {};

        // Filtreleme fonksiyonları
        document.addEventListener('DOMContentLoaded', function() {
            const categoryFilter = document.getElementById('category-filter');
            const technologyFilter = document.getElementById('technology-filter');
            const statusFilter = document.getElementById('status-filter');
            const filterReset = document.getElementById('filter-reset');
            const projectCards = document.querySelectorAll('.project-card');

            function filterProjects() {
                const selectedCategory = categoryFilter.value;
                const selectedTechnologies = Array.from(technologyFilter.selectedOptions).map(option => option.value);
                const selectedStatus = statusFilter.value;

                projectCards.forEach(card => {
                    const category = card.dataset.category;
                    const technologies = card.dataset.technologies.split(',').map(tech => tech.trim());
                    const status = card.dataset.status;

                    const categoryMatch = !selectedCategory || category === selectedCategory;
                    const technologyMatch = selectedTechnologies.length === 0 || 
                                          selectedTechnologies.every(tech => technologies.includes(tech));
                    const statusMatch = !selectedStatus || status === selectedStatus;

                    card.style.display = categoryMatch && technologyMatch && statusMatch ? 'block' : 'none';
                });
            }

            // Event listeners
            categoryFilter.addEventListener('change', filterProjects);
            technologyFilter.addEventListener('change', filterProjects);
            statusFilter.addEventListener('change', filterProjects);
            filterReset.addEventListener('click', function() {
                categoryFilter.value = '';
                technologyFilter.value = '';
                statusFilter.value = '';
                filterProjects();
            });

            // İstatistik grafiklerini güncelle
            function updateCharts() {
                const categoryChart = document.getElementById('category-chart');
                const technologyChart = document.getElementById('technology-chart');

                // Kategori grafiğini güncelle
                Object.entries(categoryStats).forEach(([category, count]) => {
                    const bar = categoryChart.querySelector(`[data-category="${category}"]`);
                    if (bar) {
                        const percentage = (count / projects.length) * 100;
                        bar.style.width = `${percentage}%`;
                        bar.querySelector('.chart-value').textContent = count;
                    }
                });

                // Teknoloji grafiğini güncelle
                Object.entries(technologyStats).forEach(([tech, count]) => {
                    const bar = technologyChart.querySelector(`[data-technology="${tech}"]`);
                    if (bar) {
                        const percentage = (count / projects.length) * 100;
                        bar.style.width = `${percentage}%`;
                        bar.querySelector('.chart-value').textContent = count;
                    }
                });
            }

            updateCharts();
        });
    </script>
</body>
</html> 