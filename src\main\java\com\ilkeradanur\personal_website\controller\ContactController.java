package com.ilkeradanur.personal_website.controller;

import com.ilkeradanur.personal_website.entity.Message;
import com.ilkeradanur.personal_website.service.MessageService;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import com.ilkeradanur.personal_website.dto.ContactFormDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Controller
@RequestMapping("/contact")
public class ContactController {

    private static final Logger logger = LoggerFactory.getLogger(ContactController.class);

    private final MessageService messageService;

    public ContactController(MessageService messageService) {
        this.messageService = messageService;
    }

    // View endpoints
    @GetMapping
    public String contact(Model model) {
        model.addAttribute("title", "İletişim");
        return "contact";
    }

    @GetMapping("/messages")
    public String messages(Model model) {
        model.addAttribute("messages", messageService.getAllMessages());
        model.addAttribute("title", "<PERSON>elen <PERSON>");
        return "messages";
    }

    // REST API endpoints
    @GetMapping("/api")
    @ResponseBody
    public ResponseEntity<List<Message>> getAllMessages() {
        return ResponseEntity.ok(messageService.getAllMessages());
    }

    @GetMapping("/api/unread")
    @ResponseBody
    public ResponseEntity<List<Message>> getUnreadMessages() {
        return ResponseEntity.ok(messageService.getUnreadMessages());
    }

    @PostMapping("/api")
    @ResponseBody
    public ResponseEntity<Message> createMessage(@RequestBody ContactFormDTO contactFormDTO) {
        logger.info("Received contact form submission: {}", contactFormDTO);
        try {
            Message savedMessage = messageService.saveMessageFromDTO(contactFormDTO);
            return ResponseEntity.ok(savedMessage);
        } catch (Exception e) {
            // Log the error for debugging
            logger.error("Error saving message: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/api/{id}/read")
    @ResponseBody
    public ResponseEntity<Message> markAsRead(@PathVariable Long id) {
        try {
            Message message = messageService.markAsRead(id);
            return ResponseEntity.ok(message);
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/api/{id}")
    @ResponseBody
    public ResponseEntity<Void> deleteMessage(@PathVariable Long id) {
        try {
            messageService.deleteMessage(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }
} 